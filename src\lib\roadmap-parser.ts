export interface ParsedRoadmap {
  title: string;
  description: string;
  duration: string;
  status: string;
  modules: ParsedModule[];
}

export interface ParsedModule {
  order: number;
  title: string;
  description: string;
  duration: string;
  isReview: boolean;
  milestones: ParsedMilestone[];
  lessons: Parsed<PERSON>esson[];
}

export interface ParsedMilestone {
  order: number;
  title: string;
  description: string;
}

export interface ParsedLesson {
  order: number;
  title: string;
  content: string;
  duration: number;
}

export class RoadmapParser {
  private static readonly SECTION_PATTERN = /^([A-Z_]+):\s*(.+)$/;
  private static readonly MODULE_PATTERN = /^MODULE:\s*(\d+)$/;
  private static readonly MILESTONE_PATTERN = /^MILESTONE:\s*(\d+)$/;
  private static readonly LESSON_PATTERN = /^LESSON:\s*(\d+)$/;

  static parse(text: string): ParsedRoadmap {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);
    
    const roadmap: Partial<ParsedRoadmap> = {
      modules: []
    };
    
    let currentModule: Partial<ParsedModule> | null = null;
    let currentSection = '';
    
    for (const line of lines) {
      const sectionMatch = line.match(this.SECTION_PATTERN);
      
      if (sectionMatch) {
        const [, sectionType, value] = sectionMatch;
        currentSection = sectionType;
        
        switch (sectionType) {
          case 'ROADMAP':
            roadmap.title = value;
            break;
          case 'DESCRIPTION':
            roadmap.description = value;
            break;
          case 'DURATION':
            roadmap.duration = value;
            break;
          case 'STATUS':
            roadmap.status = value;
            break;
          case 'MODULE':
            if (currentModule) {
              this.finalizeModule(currentModule, roadmap.modules!);
            }
            currentModule = this.startModule(value);
            break;
          case 'MILESTONE':
            if (currentModule) {
              this.addMilestone(currentModule, value);
            }
            break;
          case 'LESSON':
            if (currentModule) {
              this.addLesson(currentModule, value);
            }
            break;
          case 'TITLE':
            if (currentSection === 'MODULE' && currentModule) {
              currentModule.title = value;
            } else if (currentSection === 'MILESTONE' && currentModule?.milestones?.length) {
              const lastMilestone = currentModule.milestones[currentModule.milestones.length - 1];
              lastMilestone.title = value;
            } else if (currentSection === 'LESSON' && currentModule?.lessons?.length) {
              const lastLesson = currentModule.lessons[currentModule.lessons.length - 1];
              lastLesson.title = value;
            }
            break;
          case 'CONTENT':
            if (currentSection === 'LESSON' && currentModule?.lessons?.length) {
              const lastLesson = currentModule.lessons[currentModule.lessons.length - 1];
              lastLesson.content = value;
            }
            break;
          case 'TYPE':
            if (currentSection === 'MODULE' && currentModule) {
              currentModule.isReview = value.toLowerCase() === 'review';
            }
            break;
        }
      } else if (currentSection === 'TITLE' || currentSection === 'DESCRIPTION' || currentSection === 'CONTENT') {
        // Handle multi-line content
        if (currentSection === 'CONTENT' && currentModule?.lessons?.length) {
          const lastLesson = currentModule.lessons[currentModule.lessons.length - 1];
          lastLesson.content += '\n' + line;
        }
      }
    }
    
    // Add the last module if it exists
    if (currentModule) {
      this.finalizeModule(currentModule, roadmap.modules!);
    }
    
    return {
      title: roadmap.title || 'Untitled Roadmap',
      description: roadmap.description || '',
      duration: roadmap.duration || '',
      status: roadmap.status || 'active',
      modules: roadmap.modules || []
    };
  }
  
  private static startModule(orderStr: string): Partial<ParsedModule> {
    return {
      order: parseInt(orderStr, 10),
      milestones: [],
      lessons: [],
      isReview: false
    };
  }
  
  private static addMilestone(module: Partial<ParsedModule>, orderStr: string): void {
    const milestone: ParsedMilestone = {
      order: parseInt(orderStr, 10),
      title: '',
      description: ''
    };
    module.milestones!.push(milestone);
  }
  
  private static addLesson(module: Partial<ParsedModule>, orderStr: string): void {
    const lesson: ParsedLesson = {
      order: parseInt(orderStr, 10),
      title: '',
      content: '',
      duration: 0
    };
    module.lessons!.push(lesson);
  }
  
  private static finalizeModule(moduleItem: Partial<ParsedModule>, modules: ParsedModule[]): void {
    if (moduleItem.title && moduleItem.order) {
      modules.push({
        order: moduleItem.order,
        title: moduleItem.title,
        description: moduleItem.description || '',
        duration: moduleItem.duration || '',
        isReview: moduleItem.isReview || false,
        milestones: moduleItem.milestones || [],
        lessons: moduleItem.lessons || []
      });
    }
  }
  
  static generate(roadmap: ParsedRoadmap): string {
    let output = '';
    
    // Roadmap header
    output += `ROADMAP: ${roadmap.title}\n`;
    output += `DESCRIPTION: ${roadmap.description}\n`;
    output += `DURATION: ${roadmap.duration}\n`;
    output += `STATUS: ${roadmap.status}\n\n`;
    
    // Modules
    for (const moduleItem of roadmap.modules) {
      output += `MODULE: ${moduleItem.order}\n`;
      output += `TITLE: ${moduleItem.title}\n`;
      output += `DESCRIPTION: ${moduleItem.description}\n`;
      output += `DURATION: ${moduleItem.duration}\n`;
      output += `TYPE: ${moduleItem.isReview ? 'review' : 'regular'}\n\n`;
      
      // Milestones
      for (const milestone of moduleItem.milestones) {
        output += `MILESTONE: ${milestone.order}\n`;
        output += `TITLE: ${milestone.title}\n`;
        output += `DESCRIPTION: ${milestone.description}\n\n`;
      }
      
      // Lessons
      for (const lesson of moduleItem.lessons) {
        output += `LESSON: ${lesson.order}\n`;
        output += `TITLE: ${lesson.title}\n`;
        output += `CONTENT: ${lesson.content}\n`;
        output += `DURATION: ${lesson.duration}\n\n`;
      }
    }
    
    return output;
  }
}