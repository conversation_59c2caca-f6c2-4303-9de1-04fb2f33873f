import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { RoadmapParser } from '@/lib/roadmap-parser';
import { AIService } from '@/lib/ai-service';

export async function POST(request: NextRequest) {
  try {
    const { userId, proficiencyLevel, learningGoals, timeCommitment } = await request.json();
    
    if (!userId || !proficiencyLevel || !learningGoals || !timeCommitment) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await db.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create or update learning profile
    await db.learningProfile.upsert({
      where: { userId },
      update: {
        proficiencyLevel,
        learningGoals,
        timeCommitment,
      },
      create: {
        userId,
        proficiencyLevel,
        learningGoals,
        timeCommitment,
      },
    });

    // Initialize AI service
    const aiService = await AIService.getInstance();
    
    // Step 1: Generate roadmap structure (first API call)
    const roadmapStructure = await aiService.generateRoadmapStructure({
      proficiencyLevel,
      learningGoals,
      timeCommitment,
    });

    // Parse the generated roadmap structure
    const parsedRoadmap = RoadmapParser.parse(roadmapStructure);

    // Step 2: Generate detailed content for each lesson (multiple API calls)
    for (const moduleItem of parsedRoadmap.modules) {
      for (const lesson of moduleItem.lessons) {
        const previousLessons = moduleItem.lessons
          .filter(l => l.order < lesson.order)
          .map(l => l.title);
        
        const detailedContent = await aiService.generateLessonContent({
          title: lesson.title,
          moduleContext: moduleItem.title,
          proficiencyLevel,
          previousLessons,
        });
        
        lesson.content = detailedContent;
      }
    }

    // Step 3: Generate review content for review modules (additional API calls)
    for (const moduleItem of parsedRoadmap.modules) {
      if (moduleItem.isReview) {
        const moduleTopics = moduleItem.lessons.map(l => l.title);
        const reviewContent = await aiService.generateReviewContent({
          moduleTopics,
          userProgress: 0, // New roadmap, no progress yet
          difficulty: proficiencyLevel,
        });
        
        // Add review content as the first lesson in the review module
        if (moduleItem.lessons.length > 0) {
          moduleItem.lessons[0].content = reviewContent;
        }
      }
    }

    // Create roadmap in database
    const roadmap = await db.roadmap.create({
      data: {
        userId,
        title: parsedRoadmap.title,
        description: parsedRoadmap.description,
        totalDuration: parsedRoadmap.duration,
        status: parsedRoadmap.status,
        modules: {
          create: parsedRoadmap.modules.map(module => ({
            order: module.order,
            title: module.title,
            description: module.description,
            duration: module.duration,
            isReview: module.isReview,
            milestones: {
              create: module.milestones.map(milestone => ({
                order: milestone.order,
                title: milestone.title,
                description: milestone.description,
              }))
            },
            lessons: {
              create: module.lessons.map(lesson => ({
                order: lesson.order,
                title: lesson.title,
                content: lesson.content,
                duration: lesson.duration,
              }))
            }
          }))
        }
      },
      include: {
        modules: {
          include: {
            milestones: true,
            lessons: true,
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    // Create initial progress tracking
    await db.roadmapProgress.create({
      data: {
        roadmapId: roadmap.id,
        userId,
        progress: 0,
        startedAt: new Date(),
      }
    });

    return NextResponse.json({
      success: true,
      roadmap: {
        id: roadmap.id,
        title: roadmap.title,
        description: roadmap.description,
        totalDuration: roadmap.totalDuration,
        status: roadmap.status,
        modules: roadmap.modules,
      }
    });

  } catch (error) {
    console.error('Error generating roadmap:', error);
    return NextResponse.json(
      { error: 'Failed to generate roadmap' },
      { status: 500 }
    );
  }
}