'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BookOpen, Target, Clock, CheckCircle, Circle, Play } from 'lucide-react';

interface RoadmapData {
  id: string;
  title: string;
  description: string;
  totalDuration: string;
  status: string;
  modules: ModuleData[];
}

interface ModuleData {
  id: string;
  order: number;
  title: string;
  description: string;
  duration: string;
  isReview: boolean;
  milestones: MilestoneData[];
  lessons: LessonData[];
  progress?: { progress: number };
}

interface MilestoneData {
  id: string;
  order: number;
  title: string;
  description: string;
  isCompleted: boolean;
}

interface LessonData {
  id: string;
  order: number;
  title: string;
  content: string;
  duration: number;
  isCompleted: boolean;
}

export default function Home() {
  const [userId] = useState('demo-user-1'); // Demo user ID
  const [proficiencyLevel, setProficiencyLevel] = useState('');
  const [learningGoals, setLearningGoals] = useState('');
  const [timeCommitment, setTimeCommitment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [roadmap, setRoadmap] = useState<RoadmapData | null>(null);
  const [error, setError] = useState('');

  const handleGenerateRoadmap = async () => {
    if (!proficiencyLevel || !learningGoals || !timeCommitment) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/roadmaps/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          proficiencyLevel,
          learningGoals,
          timeCommitment,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setRoadmap(data.roadmap);
      } else {
        setError(data.error || 'Failed to generate roadmap');
      }
    } catch (err) {
      setError('An error occurred while generating the roadmap');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateOverallProgress = (modules: ModuleData[]) => {
    if (modules.length === 0) return 0;
    const totalProgress = modules.reduce((sum, module) => sum + (module.progress?.progress || 0), 0);
    return Math.round(totalProgress / modules.length);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Japanese Learning Roadmap Generator
          </h1>
          <p className="text-lg text-gray-600">
            Create personalized learning paths to master Japanese
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Input Form */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Create Your Roadmap
                </CardTitle>
                <CardDescription>
                  Tell us about your learning goals and preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Proficiency Level
                  </label>
                  <Select value={proficiencyLevel} onValueChange={setProficiencyLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Learning Goals
                  </label>
                  <Textarea
                    placeholder="e.g., Pass JLPT N5, Daily conversation, Business Japanese"
                    value={learningGoals}
                    onChange={(e) => setLearningGoals(e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Time Commitment
                  </label>
                  <Input
                    placeholder="e.g., 30 mins/day, 2 hours/week"
                    value={timeCommitment}
                    onChange={(e) => setTimeCommitment(e.target.value)}
                  />
                </div>

                {error && (
                  <div className="text-red-500 text-sm">{error}</div>
                )}

                <Button 
                  onClick={handleGenerateRoadmap} 
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? 'Generating...' : 'Generate Roadmap'}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Roadmap Display */}
          <div className="lg:col-span-2">
            {roadmap ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        {roadmap.title}
                      </CardTitle>
                      <CardDescription>{roadmap.description}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span className="text-sm">{roadmap.totalDuration}</span>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-gray-600">
                        {calculateOverallProgress(roadmap.modules)}%
                      </span>
                    </div>
                    <Progress value={calculateOverallProgress(roadmap.modules)} />
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="roadmap" className="w-full">
                    <TabsList>
                      <TabsTrigger value="roadmap">Roadmap View</TabsTrigger>
                      <TabsTrigger value="modules">Module Details</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="roadmap" className="mt-4">
                      <div className="space-y-4">
                        {roadmap.modules.map((module, index) => (
                          <div key={module.id} className="relative">
                            {/* Connection line */}
                            {index < roadmap.modules.length - 1 && (
                              <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300"></div>
                            )}
                            
                            <Card className={`ml-8 ${module.isReview ? 'border-orange-200 bg-orange-50' : ''}`}>
                              <CardHeader className="pb-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className={`w-4 h-4 rounded-full border-2 ${
                                      module.progress?.progress === 100 
                                        ? 'bg-green-500 border-green-500' 
                                        : 'bg-white border-blue-500'
                                    }`}>
                                      {module.progress?.progress === 100 && (
                                        <CheckCircle className="h-3 w-3 text-white" />
                                      )}
                                    </div>
                                    <div>
                                      <CardTitle className="text-lg flex items-center gap-2">
                                        {module.title}
                                        {module.isReview && (
                                          <Badge variant="secondary" className="text-xs">
                                            Review
                                          </Badge>
                                        )}
                                      </CardTitle>
                                      <CardDescription>{module.description}</CardDescription>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-sm font-medium">{module.duration}</div>
                                    <div className="text-xs text-gray-500">
                                      {module.progress?.progress || 0}% complete
                                    </div>
                                  </div>
                                </div>
                              </CardHeader>
                              
                              <CardContent className="pt-0">
                                <div className="space-y-2">
                                  <div className="text-sm font-medium text-gray-700">Milestones:</div>
                                  <div className="flex flex-wrap gap-2">
                                    {module.milestones.map((milestone) => (
                                      <Badge 
                                        key={milestone.id} 
                                        variant={milestone.isCompleted ? "default" : "outline"}
                                        className="text-xs"
                                      >
                                        {milestone.title}
                                      </Badge>
                                    ))}
                                  </div>
                                  
                                  <div className="text-sm font-medium text-gray-700 mt-3">Lessons:</div>
                                  <div className="text-xs text-gray-600">
                                    {module.lessons.length} lessons ({module.lessons.reduce((sum, lesson) => sum + lesson.duration, 0)} mins total)
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="modules" className="mt-4">
                      <div className="space-y-4">
                        {roadmap.modules.map((module) => (
                          <Card key={module.id}>
                            <CardHeader>
                              <CardTitle className="flex items-center justify-between">
                                <span>Module {module.order}: {module.title}</span>
                                <Badge variant={module.isReview ? "secondary" : "default"}>
                                  {module.isReview ? "Review" : "Regular"}
                                </Badge>
                              </CardTitle>
                              <CardDescription>{module.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                <div>
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Progress</span>
                                    <span className="text-sm text-gray-600">
                                      {module.progress?.progress || 0}%
                                    </span>
                                  </div>
                                  <Progress value={module.progress?.progress || 0} />
                                </div>
                                
                                <div>
                                  <h4 className="font-medium mb-2">Milestones</h4>
                                  <div className="space-y-2">
                                    {module.milestones.map((milestone) => (
                                      <div key={milestone.id} className="flex items-center gap-2">
                                        {milestone.isCompleted ? (
                                          <CheckCircle className="h-4 w-4 text-green-500" />
                                        ) : (
                                          <Circle className="h-4 w-4 text-gray-400" />
                                        )}
                                        <span className="text-sm">{milestone.title}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="font-medium mb-2">Lessons</h4>
                                  <div className="space-y-2 max-h-40 overflow-y-auto">
                                    {module.lessons.map((lesson) => (
                                      <div key={lesson.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <div className="flex items-center gap-2">
                                          {lesson.isCompleted ? (
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                          ) : (
                                            <Play className="h-4 w-4 text-blue-500" />
                                          )}
                                          <span className="text-sm">{lesson.title}</span>
                                        </div>
                                        <span className="text-xs text-gray-500">{lesson.duration}m</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Roadmap Generated Yet
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Fill in your learning preferences and generate your personalized Japanese learning roadmap
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}