import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const roadmap = await db.roadmap.findUnique({
      where: { id: params.id },
      include: {
        modules: {
          include: {
            milestones: true,
            lessons: {
              include: {
                progress: {
                  where: { userId },
                  take: 1,
                }
              },
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: { userId },
              take: 1,
            }
          },
          orderBy: {
            order: 'asc'
          }
        },
        progress: {
          where: { userId },
          take: 1,
        }
      }
    });

    if (!roadmap) {
      return NextResponse.json(
        { error: 'Roadmap not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      roadmap
    });

  } catch (error) {
    console.error('Error fetching roadmap:', error);
    return NextResponse.json(
      { error: 'Failed to fetch roadmap' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, status } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const roadmap = await db.roadmap.update({
      where: { id: params.id },
      data: {
        ...(status && { status }),
        updatedAt: new Date(),
      },
      include: {
        modules: {
          include: {
            milestones: true,
            lessons: true,
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      roadmap
    });

  } catch (error) {
    console.error('Error updating roadmap:', error);
    return NextResponse.json(
      { error: 'Failed to update roadmap' },
      { status: 500 }
    );
  }
}