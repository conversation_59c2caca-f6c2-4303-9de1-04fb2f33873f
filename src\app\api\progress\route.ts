import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { userId, type, itemId, isCompleted, score, timeSpent } = await request.json();

    if (!userId || !type || !itemId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    let progress;
    const now = new Date();

    switch (type) {
      case 'lesson':
        progress = await db.lessonProgress.upsert({
          where: {
            lessonId_userId: {
              lessonId: itemId,
              userId,
            }
          },
          update: {
            isCompleted: isCompleted !== undefined ? isCompleted : true,
            score: score !== undefined ? score : undefined,
            timeSpent: timeSpent !== undefined ? timeSpent : undefined,
            completedAt: isCompleted ? now : undefined,
            updatedAt: now,
          },
          create: {
            lessonId: itemId,
            userId,
            isCompleted: isCompleted !== undefined ? isCompleted : true,
            score: score !== undefined ? score : undefined,
            timeSpent: timeSpent !== undefined ? timeSpent : undefined,
            startedAt: now,
            completedAt: isCompleted ? now : undefined,
          }
        });

        // Update module progress
        await updateModuleProgress(userId, itemId);
        break;

      case 'module':
        progress = await db.moduleProgress.upsert({
          where: {
            moduleId_userId: {
              moduleId: itemId,
              userId,
            }
          },
          update: {
            progress: isCompleted ? 100 : undefined,
            completedAt: isCompleted ? now : undefined,
            updatedAt: now,
          },
          create: {
            moduleId: itemId,
            userId,
            progress: isCompleted ? 100 : 0,
            startedAt: now,
            completedAt: isCompleted ? now : undefined,
          }
        });

        // Update roadmap progress
        await updateRoadmapProgress(userId, itemId);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid progress type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      progress
    });

  } catch (error) {
    console.error('Error updating progress:', error);
    return NextResponse.json(
      { error: 'Failed to update progress' },
      { status: 500 }
    );
  }
}

async function updateModuleProgress(userId: string, lessonId: string) {
  // Get the module for this lesson
  const lesson = await db.lesson.findUnique({
    where: { id: lessonId },
    include: {
      moduleItem: {
        include: {
          lessons: {
            include: {
              progress: {
                where: { userId },
              }
            }
          }
        }
      }
    }
  });

  if (!lesson?.moduleItem) return;

  const moduleLessons = lesson.moduleItem.lessons;
  const completedLessons = moduleLessons.filter(l => 
    l.progress.some(p => p.isCompleted)
  ).length;

  const totalLessons = moduleLessons.length;
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  await db.moduleProgress.upsert({
    where: {
      moduleId_userId: {
        moduleId: lesson.moduleItem.id,
        userId,
      }
    },
    update: {
      progress: progressPercentage,
      completedAt: progressPercentage === 100 ? new Date() : undefined,
      updatedAt: new Date(),
    },
    create: {
      moduleId: lesson.moduleItem.id,
      userId,
      progress: progressPercentage,
      startedAt: new Date(),
      completedAt: progressPercentage === 100 ? new Date() : undefined,
    }
  });
}

async function updateRoadmapProgress(userId: string, moduleId: string) {
  // Get the roadmap for this module
  const moduleItem = await db.module.findUnique({
    where: { id: moduleId },
    include: {
      roadmap: {
        include: {
          modules: {
            include: {
              progress: {
                where: { userId },
              }
            }
          }
        }
      }
    }
  });

  if (!moduleItem?.roadmap) return;

  const roadmapModules = moduleItem.roadmap.modules;
  const completedModules = roadmapModules.filter(m => 
    m.progress.some(p => p.progress === 100)
  ).length;

  const totalModules = roadmapModules.length;
  const progressPercentage = totalModules > 0 ? (completedModules / totalModules) * 100 : 0;

  await db.roadmapProgress.upsert({
    where: {
      roadmapId_userId: {
        roadmapId: moduleItem.roadmap.id,
        userId,
      }
    },
    update: {
      progress: progressPercentage,
      completedAt: progressPercentage === 100 ? new Date() : undefined,
      updatedAt: new Date(),
    },
    create: {
      roadmapId: moduleItem.roadmap.id,
      userId,
      progress: progressPercentage,
      startedAt: new Date(),
      completedAt: progressPercentage === 100 ? new Date() : undefined,
    }
  });
}