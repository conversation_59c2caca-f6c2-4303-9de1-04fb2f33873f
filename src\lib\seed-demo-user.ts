import { db } from '@/lib/db';

async function seedDemoUser() {
  try {
    // Check if demo user already exists
    const existingUser = await db.user.findUnique({
      where: { id: 'demo-user-1' }
    });

    if (existingUser) {
      console.log('Demo user already exists');
      return;
    }

    // Create demo user
    const user = await db.user.create({
      data: {
        id: 'demo-user-1',
        email: '<EMAIL>',
        name: 'Demo User',
      }
    });

    console.log('Demo user created:', user);
  } catch (error) {
    console.error('Error creating demo user:', error);
  }
}

// Run the seed function
seedDemoUser()
  .then(() => {
    console.log('Demo user seeding completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Demo user seeding failed:', error);
    process.exit(1);
  });