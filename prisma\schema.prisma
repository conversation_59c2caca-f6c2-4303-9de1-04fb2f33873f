// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  learningProfile  LearningProfile?
  roadmaps         Roadmap[]
  roadmapProgress  RoadmapProgress[]
  moduleProgress   ModuleProgress[]
  lessonProgress   LessonProgress[]
}

model LearningProfile {
  id               String   @id @default(cuid())
  userId           String   @unique
  proficiencyLevel String   // "beginner", "intermediate", "advanced"
  learningGoals    String   // e.g., "pass JLPT N5", "daily conversation", "business Japanese"
  timeCommitment   String   // e.g., "30 mins/day"
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Roadmap {
  id               String   @id @default(cuid())
  userId           String
  title            String
  description      String
  totalDuration    String   // e.g., "12 weeks"
  status           String   @default("active") // "active", "completed", "paused"
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user     User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  modules  Module[]
  progress RoadmapProgress[]
}

model Module {
  id           String   @id @default(cuid())
  roadmapId    String
  title        String
  description  String
  order        Int
  duration     String   // e.g., "2 weeks"
  isReview     Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  roadmap   Roadmap      @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
  lessons   Lesson[]
  milestones Milestone[]
  progress  ModuleProgress[]
}

model Milestone {
  id          String   @id @default(cuid())
  moduleId    String
  title       String
  description String
  order       Int
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  module Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
}

model Lesson {
  id          String   @id @default(cuid())
  moduleId    String
  title       String
  content     String   // Custom formatted content (not JSON/XML)
  order       Int
  duration    Int      // in minutes
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  module  Module        @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  progress LessonProgress[]
}

model RoadmapProgress {
  id          String   @id @default(cuid())
  roadmapId   String
  userId      String
  progress    Float    @default(0) // 0-100 percentage
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  roadmap Roadmap @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([roadmapId, userId])
}

model ModuleProgress {
  id          String   @id @default(cuid())
  moduleId    String
  userId      String
  progress    Float    @default(0) // 0-100 percentage
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  module Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([moduleId, userId])
}

model LessonProgress {
  id          String   @id @default(cuid())
  lessonId    String
  userId      String
  isCompleted Boolean  @default(false)
  score       Float?   // 0-100
  timeSpent   Int?     // in minutes
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([lessonId, userId])
}