// Test script to verify z.ai integration is working
const ZAI = require('z-ai-web-dev-sdk');

async function testAIIntegration() {
  try {
    console.log('Testing z.ai integration...');
    const zai = await ZAI.default.create();
    
    const response = await zai.chat.completions.create({
      model: 'glm-4.5-flash',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: 'Say "Hello, z.ai integration is working!" in Japanese.'
        }
      ],
      temperature: 0.7,
      max_tokens: 100,
    });

    console.log('Response received:', response);
    
    // Handle different response structures
    let content = '';
    if (response.choices && response.choices[0]?.message?.content) {
      content = response.choices[0].message.content;
    } else if (response.content) {
      content = response.content;
    } else if (response.message) {
      content = response.message;
    } else if (typeof response === 'string') {
      content = response;
    }
    
    console.log('AI Response:', content);
    console.log('✅ z.ai integration test successful!');
    
  } catch (error) {
    console.error('❌ z.ai integration test failed:', error.message);
    console.error('Full error:', error);
  }
}

testAIIntegration();
